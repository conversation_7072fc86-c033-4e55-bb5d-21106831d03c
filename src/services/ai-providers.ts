
// AI Provider interfaces and types
export interface AIProviderConfig {
  apiKey: string;
  model?: string;
}

export interface AIResponse {
  content: string;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export interface OpenAIResponse {
  choices: Array<{
    message: {
      content: string;
      reasoning_content?: string;
    };
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  error?: {
    message: string;
  };
}

export interface MoonshotResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  error?: {
    message: string;
  };
}

export interface ClaudeResponse {
  content: string;
  error?: {
    message: string;
  };
}

export interface GeminiResponse {
  candidates?: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
    };
  }>;
  usageMetadata?: {
    promptTokenCount: number;
    candidatesTokenCount: number;
    totalTokenCount: number;
  };
  promptFeedback?: {
    tokenCount?: {
      promptTokens: number;
      totalTokens: number;
    };
  };
  error?: {
    message: string;
  };
}

export interface OpenRouterResponse {
  choices: Array<{
    message: {
      content: string;
      reasoning_content?: string;
    };
    error?: {
      message: string;
    };
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// Custom error class
export class AIProviderError extends Error {
  constructor(public status: number, message: string) {
    super(message);
    this.name = 'AIProviderError';
  }
}

// Stream chunk interface for streaming responses
export interface StreamChunk {
  delta: string;
  isComplete: boolean;
  reasoning?: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  metadata?: any;
}

// Abstract base class for AI providers
export abstract class AIProvider {
  constructor(protected config: AIProviderConfig) {}
  abstract generateContent(prompt: string): Promise<AIResponse>;

  // Optional streaming support - providers can override this
  generateContentStream?(prompt: string): Promise<AsyncIterable<StreamChunk>>;
}

// OpenAI Provider
export class OpenAIProvider extends AIProvider {
  async generateContent(prompt: string): Promise<AIResponse> {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`
      },
      body: JSON.stringify({
        model: this.config.model || 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are a helpful assistant that generates form content based on descriptions. Always return valid JSON.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7
      })
    });

    const data = await response.json() as OpenAIResponse;

    if (!response.ok) {
      throw new AIProviderError(response.status, data.error?.message || 'OpenAI API error');
    }

    const message = data.choices[0].message;
    const content = message.content;
    const reasoningContent = (message as any).reasoning_content;

    // 如果有推理内容，将其包装在推理标签中
    let finalContent = content;
    if (reasoningContent) {
      finalContent = `<thinking>\n${reasoningContent}\n</thinking>\n\n${content}`;
    }

    return {
      content: finalContent,
      usage: data.usage
    };
  }
}

// Moonshot Provider
export class MoonshotProvider extends AIProvider {
  async generateContent(prompt: string): Promise<AIResponse> {
    const response = await fetch('https://api.moonshot.cn/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`
      },
      body: JSON.stringify({
        model: this.config.model || 'moonshot-v1-8k',
        messages: [
          {
            role: 'system',
            content: 'You are a helpful assistant that generates form content based on descriptions. Always return valid JSON.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7
      })
    });

    const data = await response.json() as MoonshotResponse;

    if (!response.ok) {
      throw new AIProviderError(response.status, data.error?.message || 'Moonshot API error');
    }

    const content = data.choices[0].message.content;

    return {
      content: content,
      usage: data.usage
    };
  }
}

// Claude Provider
export class ClaudeProvider extends AIProvider {
  async generateContent(prompt: string): Promise<AIResponse> {
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': this.config.apiKey,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify({
        model: this.config.model || 'claude-3-sonnet-20240229',
        max_tokens: 1000,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ]
      })
    });

    const data = await response.json() as any;

    if (!response.ok) {
      throw new AIProviderError(response.status, data.error?.message || 'Claude API error');
    }

    const content = data.content[0].text;

    return {
      content: content,
      usage: {
        prompt_tokens: data.usage?.input_tokens || 0,
        completion_tokens: data.usage?.output_tokens || 0,
        total_tokens: (data.usage?.input_tokens || 0) + (data.usage?.output_tokens || 0)
      }
    };
  }
}

// DeepSeek Provider
export class DeepSeekProvider extends AIProvider {
  async generateContent(prompt: string): Promise<AIResponse> {
    const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`
      },
      body: JSON.stringify({
        model: this.config.model || 'deepseek-chat',
        messages: [
          {
            role: 'system',
            content: 'You are a helpful assistant that generates form content based on descriptions. Always return valid JSON.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7
      })
    });

    const data = await response.json();

    if (!response.ok) {
      throw new AIProviderError(response.status, data.error?.message || 'Deepseek API error');
    }

    const message = data.choices[0].message;
    const content = message.content;
    const reasoningContent = message.reasoning_content;

    // 如果有推理内容，将其包装在推理标签中，这样后续的推理解析器可以识别
    let finalContent = content;
    if (reasoningContent) {
      finalContent = `<think>\n${reasoningContent}\n</think>\n\n${content}`;
    }

    return {
      content: finalContent,
      usage: data.usage
    };
  }
}

// Gemini Provider
export class GeminiProvider extends AIProvider {
  async generateContent(prompt: string): Promise<AIResponse> {
    const apiKey = this.config.apiKey;
    const model = this.config.model || 'gemini-2.0-flash';
    const response = await fetch(`https://generativelanguage.googleapis.com/v1/models/${model}:generateContent?key=${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        contents: [
          {
            role: "user",
            parts: [
              {
                text: `You are a helpful assistant that generates form content based on descriptions. Always return valid JSON object without any markdown formatting or additional text. Here's the task:\n\n${prompt}`
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.7,
          topP: 0.8,
          topK: 40
        }
      })
    });

    const data = await response.json() as GeminiResponse;

    if (!response.ok) {
      throw new AIProviderError(response.status, data.error?.message || 'Gemini API error');
    }

    if (!data.candidates?.[0]?.content?.parts?.[0]?.text) {
      throw new AIProviderError(400, 'Invalid response from Gemini API');
    }

    const rawContent = data.candidates[0].content.parts[0].text;

    // Try to clean and parse content
    try {
      // Remove possible markdown markers
      let cleanContent = rawContent.replace(/^```json\s*/, '').replace(/```\s*$/, '');

      // Remove possible comments
      cleanContent = cleanContent.replace(/\/\*[\s\S]*?\*\/|\/\/.*/g, '');

      // Try to parse to ensure it's valid JSON
      const parsedContent = JSON.parse(cleanContent);

      return {
        content: JSON.stringify(parsedContent),
        usage: {
          prompt_tokens: data.usageMetadata?.promptTokenCount || data.promptFeedback?.tokenCount?.promptTokens || 0,
          completion_tokens: data.usageMetadata?.candidatesTokenCount || 0,
          total_tokens: data.usageMetadata?.totalTokenCount || data.promptFeedback?.tokenCount?.totalTokens || 0
        }
      };
    } catch (error) {
      console.error('Gemini raw response:', rawContent);
      throw new AIProviderError(400, 'Failed to parse Gemini response as JSON');
    }
  }
}

// OpenRouter Provider
export class OpenRouterProvider extends AIProvider {
  async generateContent(prompt: string): Promise<AIResponse> {
    // Default to OpenAI's GPT-3.5 Turbo model, but can specify other models via config.model
    // OpenRouter supports multiple models in "provider/model" format, such as:
    // - openai/gpt-4o
    // - anthropic/claude-3-opus
    // - google/gemini-pro
    // - meta-llama/llama-3-70b-instruct
    const model = this.config.model || 'openai/gpt-3.5-turbo';

    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`,
        'HTTP-Referer': 'https://fillify.tech', // Can be replaced with your website URL
        'X-Title': 'Fillify' // Can be replaced with your app name
      },
      body: JSON.stringify({
        model: model,
        messages: [
          {
            role: 'system',
            content: 'You are a helpful assistant that generates form content based on descriptions. Always return valid JSON without any markdown formatting or additional text.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 1000,
        response_format: { type: 'json_object' } // Request JSON format response
      })
    });

    const data = await response.json() as OpenRouterResponse;

    if (!response.ok) {
      const errorMessage = data.choices?.[0]?.error?.message || 'OpenRouter API error';
      throw new AIProviderError(response.status, errorMessage);
    }

    if (!data.choices || data.choices.length === 0 || !data.choices[0].message.content) {
      throw new AIProviderError(400, 'Invalid response from OpenRouter API');
    }

    const message = data.choices[0].message;
    const rawContent = message.content;
    const reasoningContent = message.reasoning_content;

    // Try to clean and parse content
    try {
      let cleanContent = rawContent;

      // Remove possible markdown markers
      cleanContent = cleanContent.replace(/^```json\s*/, '').replace(/```\s*$/, '');

      // Remove possible comments
      cleanContent = cleanContent.replace(/\/\*[\s\S]*?\*\/|\/\/.*/g, '');

      // Try to parse to ensure it's valid JSON
      const parsedContent = JSON.parse(cleanContent);

      // 如果有推理内容，将其包装在推理标签中
      let finalContent = JSON.stringify(parsedContent);
      if (reasoningContent) {
        finalContent = `<think>\n${reasoningContent}\n</think>\n\n${finalContent}`;
      }

      return {
        content: finalContent,
        usage: {
          prompt_tokens: data.usage.prompt_tokens,
          completion_tokens: data.usage.completion_tokens,
          total_tokens: data.usage.total_tokens
        }
      };
    } catch (error) {
      console.error('OpenRouter raw response:', rawContent);
      throw new AIProviderError(400, 'Failed to parse OpenRouter response as JSON');
    }
  }
}

// Ollama Provider
export class OllamaProvider extends AIProvider {
  private endpoint: string;

  constructor(config: AIProviderConfig & { endpoint?: string }) {
    super(config);
    this.endpoint = config.endpoint || 'http://localhost:11434';
  }

  async generateContent(prompt: string): Promise<AIResponse> {
    const response = await fetch(`${this.endpoint}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ollama',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        model: this.config.model || 'llama2',
        messages: [
          {
            role: 'system',
            content: 'You are a helpful assistant that generates form content based on descriptions. IMPORTANT: You must ALWAYS return ONLY a valid JSON object. Do not include any explanatory text, markdown formatting, or additional content. Return ONLY the JSON object.'
          },
          {
            role: 'user',
            content: prompt + '\n\nIMPORTANT: Return ONLY a valid JSON object with no additional text or formatting.'
          }
        ],
        stream: true  // 启用流式处理以支持推理内容的实时展示
      })
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Unknown error');
      if (response.status === 0 || response.status === 404) {
        throw new AIProviderError(response.status, `Cannot connect to Ollama server at ${this.endpoint}. Please ensure Ollama is running.`);
      }
      throw new AIProviderError(response.status, `Ollama server error: ${errorText}`);
    }

    const data = await response.json();

    if (!data.choices || data.choices.length === 0 || !data.choices[0].message.content) {
      throw new AIProviderError(400, 'Invalid response from Ollama server');
    }

    const rawContent = data.choices[0].message.content;

    // Try to clean and parse content with more flexible approach for Ollama
    try {
      // 检查是否包含推理内容
      const hasReasoningContent = /\\u003c(?:think|thinking|reasoning)\\u003e|<(?:think|thinking|reasoning)>/i.test(rawContent);

      if (hasReasoningContent) {
        // 如果包含推理内容，保留完整内容并让后续的推理解析器处理
        console.log('Ollama response contains reasoning content, preserving full content');
        return {
          content: rawContent, // 保留完整的原始内容，包括推理标签
          usage: {
            prompt_tokens: data.usage?.prompt_tokens || 0,
            completion_tokens: data.usage?.completion_tokens || 0,
            total_tokens: data.usage?.total_tokens || 0
          }
        };
      }

      // 如果没有推理内容，按原来的方式处理（只提取 JSON）
      let cleanContent = rawContent.trim();

      // Remove markdown code blocks
      cleanContent = cleanContent.replace(/^```(?:json)?\s*/, '').replace(/```\s*$/, '');

      // Remove any leading/trailing text that's not JSON
      const jsonMatch = cleanContent.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        cleanContent = jsonMatch[0];
      }

      // Remove comments
      cleanContent = cleanContent.replace(/\/\*[\s\S]*?\*\/|\/\/.*/g, '');

      // Clean up common formatting issues
      cleanContent = cleanContent
        .replace(/,\s*}/g, '}')  // Remove trailing commas
        .replace(/,\s*]/g, ']')  // Remove trailing commas in arrays
        .trim();

      // Try to parse the cleaned content
      const parsedContent = JSON.parse(cleanContent);

      // Debug logging for Ollama responses
      console.log('Ollama parsed content:', parsedContent);
      console.log('Ollama content keys:', Object.keys(parsedContent));

      return {
        content: JSON.stringify(parsedContent),
        usage: {
          prompt_tokens: data.usage?.prompt_tokens || 0,
          completion_tokens: data.usage?.completion_tokens || 0,
          total_tokens: data.usage?.total_tokens || 0
        }
      };
    } catch (error) {
      console.error('Ollama raw response:', rawContent);
      console.error('Ollama parsing error:', error);

      // If JSON parsing fails, try to return the raw content in a structured way
      // This is a fallback for cases where Ollama doesn't return valid JSON
      try {
        // Try to create a simple structure from the raw content
        const fallbackContent = {
          content: rawContent,
          description: rawContent
        };

        return {
          content: JSON.stringify(fallbackContent),
          usage: {
            prompt_tokens: data.usage?.prompt_tokens || 0,
            completion_tokens: data.usage?.completion_tokens || 0,
            total_tokens: data.usage?.total_tokens || 0
          }
        };
      } catch (fallbackError) {
        throw new AIProviderError(400, `Failed to parse Ollama response: ${error instanceof Error ? error.message : String(error)}`);
      }
    }
  }

  /**
   * 流式生成内容 - 支持推理模型的实时展示
   */
  async generateContentStream(prompt: string): Promise<AsyncIterable<StreamChunk>> {
    console.log('[Ollama] Starting stream request...');

    const response = await fetch(`${this.endpoint}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ollama',
        'Accept': 'text/event-stream'
      },
      body: JSON.stringify({
        model: this.config.model || 'llama2',
        messages: [
          {
            role: 'system',
            content: 'You are a helpful assistant that generates form content based on descriptions. IMPORTANT: You must ALWAYS return ONLY a valid JSON object. Do not include any explanatory text, markdown formatting, or additional content. Return ONLY the JSON object.'
          },
          {
            role: 'user',
            content: prompt + '\n\nIMPORTANT: Return ONLY a valid JSON object with no additional text or formatting.'
          }
        ],
        stream: true
      })
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Unknown error');
      if (response.status === 0 || response.status === 404) {
        throw new AIProviderError(response.status, `Cannot connect to Ollama server at ${this.endpoint}. Please ensure Ollama is running.`);
      }
      throw new AIProviderError(response.status, `Ollama server error: ${errorText}`);
    }

    console.log('[Ollama] Stream response received, processing...');
    return this.processOllamaStream(response);
  }

  /**
   * 处理 Ollama 流式响应
   */
  private async *processOllamaStream(response: Response): AsyncIterable<StreamChunk> {
    const reader = response.body?.getReader();
    if (!reader) {
      throw new AIProviderError(400, 'No readable stream available');
    }

    const decoder = new TextDecoder();
    let buffer = '';
    let chunkCount = 0;

    try {
      console.log('[Ollama] Starting to read stream...');

      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          console.log('[Ollama] Stream ended, total chunks:', chunkCount);
          break;
        }

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim() && line.startsWith('data: ')) {
            const data = line.slice(6).trim();
            if (data === '[DONE]') {
              console.log('[Ollama] Received [DONE] signal');
              yield { delta: '', isComplete: true };
              return;
            }

            try {
              const chunk = JSON.parse(data);
              const delta = chunk.choices?.[0]?.delta;

              if (!delta) continue;

              const content = delta.content || '';
              const reasoning = delta.reasoning || '';

              // 处理推理内容或常规内容
              if (reasoning) {
                chunkCount++;
                console.log(`[Ollama] Reasoning chunk ${chunkCount}:`, reasoning.substring(0, 50) + (reasoning.length > 50 ? '...' : ''));

                yield {
                  delta: reasoning,
                  isComplete: false,
                  reasoning: reasoning
                };
              } else if (content) {
                chunkCount++;
                console.log(`[Ollama] Content chunk ${chunkCount}:`, content.substring(0, 50) + (content.length > 50 ? '...' : ''));

                yield {
                  delta: content,
                  isComplete: false
                };
              }

              // 检查是否完成
              if (chunk.choices?.[0]?.finish_reason) {
                console.log('[Ollama] Stream finished with reason:', chunk.choices[0].finish_reason);
                yield { delta: '', isComplete: true };
                return;
              }
            } catch (error) {
              console.warn('[Ollama] Failed to parse stream chunk:', data, error);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
      console.log('[Ollama] Stream reader released');
    }
  }


}

// Provider factory function
export function createAIProvider(provider: string, apiKey: string, model?: string, options?: any): AIProvider {
  switch (provider?.toLowerCase()) {
    case 'openai':
      if (!apiKey) throw new AIProviderError(400, 'API key is required for OpenAI');
      return new OpenAIProvider({ apiKey, model });
    case 'moonshot':
      if (!apiKey) throw new AIProviderError(400, 'API key is required for Moonshot');
      return new MoonshotProvider({ apiKey, model });
    case 'claude':
      if (!apiKey) throw new AIProviderError(400, 'API key is required for Claude');
      return new ClaudeProvider({ apiKey, model });
    case 'gemini':
      if (!apiKey) throw new AIProviderError(400, 'API key is required for Gemini');
      // If no model specified, use default gemini-2.0-flash model
      return new GeminiProvider({ apiKey, model: model || 'gemini-2.0-flash' });
    case 'openrouter':
      if (!apiKey) throw new AIProviderError(400, 'API key is required for OpenRouter');
      // OpenRouter supports multiple models in "provider/model" format, e.g. openai/gpt-4o
      return new OpenRouterProvider({ apiKey, model });
    case 'ollama':
      // For Ollama, apiKey is actually the endpoint URL
      return new OllamaProvider({
        apiKey: 'ollama', // Ollama doesn't need a real API key
        model,
        endpoint: apiKey || options?.endpoint || 'http://localhost:11434'
      });
    case 'deepseek':
    default:
      if (!apiKey) throw new AIProviderError(400, 'API key is required for DeepSeek');
      return new DeepSeekProvider({ apiKey, model });
  }
}


